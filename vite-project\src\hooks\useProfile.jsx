import { useState, useEffect } from 'react';
import axios from 'axios';

export default function useProfile(baseURL = "http://localhost:3000/api") {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [profile, setProfile] = useState(null);

  // Mock profile data for development
  const mockProfile = {
    id: 1,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    role: 'professional',
    profileImage: null,
    
    // Professional Info
    currentCompany: 'Tech Solutions Inc.',
    currentPosition: 'Senior Software Engineer',
    experience: '5 years',
    industry: 'Technology',
    startDate: '2021-03',
    
    // Education
    university: 'Stanford University',
    degree: 'Bachelor of Science in Computer Science',
    graduationYear: '2019',
    gpa: '3.8',
    studentId: 'STU123456',
    
    // Additional Info
    bio: 'Passionate software engineer with expertise in full-stack development. Love building scalable applications and mentoring junior developers.',
    skills: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS', 'Docker'],
    achievements: [
      'Employee of the Year 2023',
      'Led team of 8 developers',
      'Published 3 research papers'
    ],
    
    // Stats
    connectionsCount: 156,
    feedbackGiven: 23,
    eventsAttended: 12,
    
    // Settings
    settings: {
      notifications: {
        emailNotifications: true,
        eventReminders: true,
        newConnections: true,
        feedbackResponses: true,
        weeklyDigest: false
      },
      privacy: {
        profileVisibility: 'public',
        showEmail: false,
        showPhone: false,
        allowMessages: true,
        showInDirectory: true
      },
      account: {
        twoFactorAuth: false,
        loginAlerts: true
      }
    }
  };

  const fetchProfile = async () => {
    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // For development, use mock data
      // In production, uncomment the API call below
      setProfile(mockProfile);
      
      /* 
      const response = await axios.get(`${baseURL}/profile`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setProfile(response.data);
      */
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to fetch profile');
      // For development, still set mock data even on error
      setProfile(mockProfile);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (profileData) => {
    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // For development, just update local state
      setProfile(prev => ({ ...prev, ...profileData }));
      
      /*
      const response = await axios.put(`${baseURL}/profile`, profileData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setProfile(response.data);
      */
      
      return true;
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to update profile');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (settingsData) => {
    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // For development, just update local state
      setProfile(prev => ({ 
        ...prev, 
        settings: { ...prev.settings, ...settingsData }
      }));
      
      /*
      const response = await axios.put(`${baseURL}/profile/settings`, settingsData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setProfile(prev => ({ ...prev, settings: response.data }));
      */
      
      return true;
    } catch (err) {
      setError(err.response?.data?.message || err.message || 'Failed to update settings');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch profile on hook initialization
  useEffect(() => {
    fetchProfile();
  }, []);

  return { 
    profile, 
    loading, 
    error, 
    fetchProfile, 
    updateProfile, 
    updateSettings 
  };
}
