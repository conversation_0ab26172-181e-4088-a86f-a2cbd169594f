import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  User, 
  MapPin, 
  Briefcase, 
  GraduationCap,
  Mail,
  Phone,
  Calendar,
  Users
} from 'lucide-react';
import DashboardLayout from './DashboardLayout';

const AlumniDirectory = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    graduationYear: '',
    industry: '',
    location: '',
    degree: ''
  });
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'

  // Mock alumni data
  const [alumni] = useState([
    {
      id: 1,
      firstName: 'Sarah',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      currentCompany: 'Google',
      currentPosition: 'Product Manager',
      industry: 'Technology',
      university: 'Stanford University',
      degree: 'Master of Business Administration',
      graduationYear: '2020',
      bio: 'Product manager with 6+ years of experience in tech startups and large corporations.',
      skills: ['Product Management', 'Strategy', 'Analytics', 'Leadership']
    },
    {
      id: 2,
      firstName: 'Michael',
      lastName: 'Chen',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Seattle, WA',
      currentCompany: 'Microsoft',
      currentPosition: 'Senior Software Engineer',
      industry: 'Technology',
      university: 'Stanford University',
      degree: 'Bachelor of Science in Computer Science',
      graduationYear: '2018',
      bio: 'Full-stack developer passionate about cloud computing and AI.',
      skills: ['JavaScript', 'Python', 'Azure', 'Machine Learning']
    },
    {
      id: 3,
      firstName: 'Emily',
      lastName: 'Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Los Angeles, CA',
      currentCompany: 'Goldman Sachs',
      currentPosition: 'Investment Analyst',
      industry: 'Finance',
      university: 'Stanford University',
      degree: 'Bachelor of Science in Economics',
      graduationYear: '2021',
      bio: 'Financial analyst specializing in equity research and portfolio management.',
      skills: ['Financial Analysis', 'Excel', 'Bloomberg', 'Risk Management']
    },
    {
      id: 4,
      firstName: 'David',
      lastName: 'Kim',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Boston, MA',
      currentCompany: 'Harvard Medical School',
      currentPosition: 'Research Scientist',
      industry: 'Healthcare',
      university: 'Stanford University',
      degree: 'PhD in Biomedical Engineering',
      graduationYear: '2019',
      bio: 'Biomedical researcher focused on developing innovative medical devices.',
      skills: ['Research', 'Bioengineering', 'Data Analysis', 'Medical Devices']
    }
  ]);

  const [filteredAlumni, setFilteredAlumni] = useState(alumni);

  // Filter options
  const filterOptions = {
    graduationYear: [...new Set(alumni.map(a => a.graduationYear))].sort(),
    industry: [...new Set(alumni.map(a => a.industry))].sort(),
    location: [...new Set(alumni.map(a => a.location.split(',')[1]?.trim() || a.location))].sort(),
    degree: [...new Set(alumni.map(a => a.degree.split(' ')[0]))].sort()
  };

  useEffect(() => {
    let filtered = alumni;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(person =>
        `${person.firstName} ${person.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        person.currentCompany.toLowerCase().includes(searchTerm.toLowerCase()) ||
        person.currentPosition.toLowerCase().includes(searchTerm.toLowerCase()) ||
        person.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply other filters
    Object.entries(selectedFilters).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(person => {
          switch (key) {
            case 'graduationYear':
              return person.graduationYear === value;
            case 'industry':
              return person.industry === value;
            case 'location':
              return person.location.includes(value);
            case 'degree':
              return person.degree.includes(value);
            default:
              return true;
          }
        });
      }
    });

    setFilteredAlumni(filtered);
  }, [searchTerm, selectedFilters, alumni]);

  const handleFilterChange = (filterType, value) => {
    setSelectedFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearFilters = () => {
    setSelectedFilters({
      graduationYear: '',
      industry: '',
      location: '',
      degree: ''
    });
    setSearchTerm('');
  };

  const AlumniCard = ({ person }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
      <div className="flex items-start space-x-4">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
          <User className="w-8 h-8 text-white" />
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {person.firstName} {person.lastName}
          </h3>
          <p className="text-blue-600 font-medium">{person.currentPosition}</p>
          <p className="text-gray-600 text-sm">{person.currentCompany}</p>
          
          <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
            <div className="flex items-center">
              <GraduationCap className="w-4 h-4 mr-1" />
              Class of {person.graduationYear}
            </div>
            <div className="flex items-center">
              <MapPin className="w-4 h-4 mr-1" />
              {person.location}
            </div>
          </div>
          
          <p className="text-gray-600 text-sm mt-2 line-clamp-2">{person.bio}</p>
          
          <div className="flex flex-wrap gap-1 mt-3">
            {person.skills.slice(0, 3).map((skill, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
              >
                {skill}
              </span>
            ))}
            {person.skills.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">
                +{person.skills.length - 3} more
              </span>
            )}
          </div>
          
          <div className="flex space-x-2 mt-4">
            <button className="flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700">
              <Mail className="w-3 h-3 mr-1" />
              Connect
            </button>
            <button className="flex items-center px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-50">
              <User className="w-3 h-3 mr-1" />
              View Profile
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <DashboardLayout activeTab="alumni">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Alumni Directory</h1>
          <p className="text-gray-600 mt-2">
            Connect with fellow alumni from your university and beyond.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search by name, company, position, or skills..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-3">
              <select
                value={selectedFilters.graduationYear}
                onChange={(e) => handleFilterChange('graduationYear', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Years</option>
                {filterOptions.graduationYear.map(year => (
                  <option key={year} value={year}>Class of {year}</option>
                ))}
              </select>

              <select
                value={selectedFilters.industry}
                onChange={(e) => handleFilterChange('industry', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Industries</option>
                {filterOptions.industry.map(industry => (
                  <option key={industry} value={industry}>{industry}</option>
                ))}
              </select>

              <button
                onClick={clearFilters}
                className="px-4 py-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-gray-600">
            Showing {filteredAlumni.length} of {alumni.length} alumni
          </p>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
            >
              <div className="w-4 h-4 grid grid-cols-2 gap-0.5">
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
              </div>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
            >
              <div className="w-4 h-4 space-y-1">
                <div className="h-0.5 bg-current rounded"></div>
                <div className="h-0.5 bg-current rounded"></div>
                <div className="h-0.5 bg-current rounded"></div>
              </div>
            </button>
          </div>
        </div>

        {/* Alumni Grid/List */}
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1'
        }`}>
          {filteredAlumni.map((person) => (
            <AlumniCard key={person.id} person={person} />
          ))}
        </div>

        {filteredAlumni.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No alumni found</h3>
            <p className="text-gray-600">Try adjusting your search criteria or filters.</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default AlumniDirectory;
