// src/hooks/useSignup.js
import { useState } from "react";
import axios from "axios";

export default function useSignup(baseURL = "http://localhost:3000/api") {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [newUser, setNewUser] = useState(null);

  const signup = async (formData) => {
    setLoading(true);
    setError(null);

    try {
      const res = await axios.post(`${baseURL}/signup`, {
        firstName :formData.firstName,
 lastName :formData.lastName,
 email :formData.email,
 password : formData.password,
 phone :formData.phone,
 studentId :formData.studentId,
 university : formData.university
      });
      
      // If backend returns token, store it
      if (res.data.token) {
        localStorage.setItem("token", res.data.token);
      }

      setNewUser(res.data.user || null);
      return res.data;
    } catch (err) {
      setError(err.response?.data?.message || "Signup failed");
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { signup, loading, error, newUser };
}
