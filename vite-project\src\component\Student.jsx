
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const Student = () => {
    const navigate = useNavigate();

    useEffect(() => {
        // Redirect to dashboard
        navigate('/dashboard');
    }, [navigate]);

    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Redirecting to dashboard...</p>
            </div>
        </div>
    );
};

export default Student;