import {createBrowserRouter} from "react-router-dom";
import RoleBasedSignup from "../component/Signup";
import LoginPage from "../component/Login";
import LoginAdmin from "../component/Login-admin";
import Student from "../component/Student";
import UserDashboard from "../component/UserDashboard";
import UserProfile from "../component/UserProfile";
import AlumniDirectory from "../component/AlumniDirectory";
import FeedbackPortal from "../component/FeedbackPortal";
import Events from "../component/Events";
import Settings from "../component/Settings";

const router = createBrowserRouter([
    {
        path : '/',element:<RoleBasedSignup/>
    },
    {
        path : '/login',
        children:[
            {path :'student',element:<LoginPage/>},
            {path:"professional",element:<LoginAdmin/>},
            {path:"instructor",element:<LoginAdmin/>}
        ]
    },
    {
        path:"/student",
        element:<Student/>
    },
    {
        path: "/dashboard",
        children: [
            { index: true, element: <UserDashboard /> },
            { path: "profile", element: <UserProfile /> },
            { path: "alumni", element: <AlumniDirectory /> },
            { path: "feedback", element: <FeedbackPortal /> },
            { path: "events", element: <Events /> },
            { path: "settings", element: <Settings /> }
        ]
    }
])
export default router;