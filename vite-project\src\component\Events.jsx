import React, { useState } from 'react';
import { 
  Calendar, 
  MapPin, 
  Clock, 
  Users, 
  ExternalLink,
  Filter,
  Search,
  Plus,
  User
} from 'lucide-react';
import DashboardLayout from './DashboardLayout';

const Events = () => {
  const [activeTab, setActiveTab] = useState('upcoming');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  const [events] = useState([
    {
      id: 1,
      title: 'Alumni Networking Night',
      description: 'Join fellow alumni for an evening of networking and professional connections.',
      date: '2024-02-15',
      time: '18:00',
      location: 'Stanford Alumni Center',
      category: 'Networking',
      attendees: 45,
      maxAttendees: 100,
      isRegistered: true,
      status: 'upcoming',
      organizer: 'Alumni Relations Office'
    },
    {
      id: 2,
      title: 'Career Development Workshop',
      description: 'Learn about the latest trends in your industry and develop your career skills.',
      date: '2024-02-20',
      time: '14:00',
      location: 'Virtual Event',
      category: 'Career',
      attendees: 78,
      maxAttendees: 150,
      isRegistered: false,
      status: 'upcoming',
      organizer: 'Career Services'
    },
    {
      id: 3,
      title: 'Tech Innovation Summit',
      description: 'Explore cutting-edge technologies and their impact on various industries.',
      date: '2024-03-05',
      time: '09:00',
      location: 'San Francisco Convention Center',
      category: 'Technology',
      attendees: 120,
      maxAttendees: 200,
      isRegistered: true,
      status: 'upcoming',
      organizer: 'Tech Alumni Group'
    },
    {
      id: 4,
      title: 'Annual Alumni Gala',
      description: 'Celebrate achievements and connect with alumni from all graduating classes.',
      date: '2024-01-10',
      time: '19:00',
      location: 'Grand Ballroom, Downtown Hotel',
      category: 'Social',
      attendees: 200,
      maxAttendees: 250,
      isRegistered: true,
      status: 'past',
      organizer: 'Alumni Relations Office'
    }
  ]);

  const categories = ['All', 'Networking', 'Career', 'Technology', 'Social', 'Education'];

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '' || selectedCategory === 'All' || event.category === selectedCategory;
    const matchesTab = activeTab === 'all' || event.status === activeTab;
    
    return matchesSearch && matchesCategory && matchesTab;
  });

  const handleRegister = (eventId) => {
    console.log('Registering for event:', eventId);
    // Here you would handle registration logic
    alert('Successfully registered for the event!');
  };

  const EventCard = ({ event }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">{event.title}</h3>
          <p className="text-gray-600 text-sm mb-4">{event.description}</p>
          
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              {new Date(event.date).toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2" />
              {event.time}
            </div>
            <div className="flex items-center">
              <MapPin className="w-4 h-4 mr-2" />
              {event.location}
            </div>
            <div className="flex items-center">
              <Users className="w-4 h-4 mr-2" />
              {event.attendees}/{event.maxAttendees} attendees
            </div>
          </div>
        </div>
        
        <div className="ml-4">
          <span className={`px-3 py-1 text-xs font-medium rounded-full ${
            event.category === 'Networking' ? 'bg-blue-100 text-blue-700' :
            event.category === 'Career' ? 'bg-green-100 text-green-700' :
            event.category === 'Technology' ? 'bg-purple-100 text-purple-700' :
            event.category === 'Social' ? 'bg-pink-100 text-pink-700' :
            'bg-gray-100 text-gray-700'
          }`}>
            {event.category}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-500">
          Organized by {event.organizer}
        </div>
        
        <div className="flex space-x-2">
          {event.status === 'upcoming' && (
            <>
              {event.isRegistered ? (
                <span className="px-4 py-2 bg-green-100 text-green-700 text-sm font-medium rounded-lg">
                  Registered
                </span>
              ) : (
                <button
                  onClick={() => handleRegister(event.id)}
                  className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
                >
                  Register
                </button>
              )}
            </>
          )}
          
          <button className="px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 flex items-center">
            <ExternalLink className="w-3 h-3 mr-1" />
            Details
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <DashboardLayout activeTab="events">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Events</h1>
          <p className="text-gray-600 mt-2">
            Discover and participate in alumni events and activities.
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <nav className="flex space-x-8 border-b border-gray-200">
            {[
              { id: 'upcoming', name: 'Upcoming Events' },
              { id: 'past', name: 'Past Events' },
              { id: 'all', name: 'All Events' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search events..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {categories.map(category => (
                <option key={category} value={category === 'All' ? '' : category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Events List */}
        <div className="space-y-6">
          {filteredEvents.length > 0 ? (
            filteredEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))
          ) : (
            <div className="text-center py-12">
              <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
              <p className="text-gray-600">Try adjusting your search criteria or check back later for new events.</p>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Events;
