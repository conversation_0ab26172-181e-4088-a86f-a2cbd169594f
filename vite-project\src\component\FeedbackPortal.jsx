import React, { useState } from 'react';
import { 
  MessageSquare, 
  Send, 
  Star, 
  ThumbsUp, 
  ThumbsDown,
  Calendar,
  User,
  Filter,
  Plus
} from 'lucide-react';
import DashboardLayout from './DashboardLayout';

const FeedbackPortal = () => {
  const [activeTab, setActiveTab] = useState('submit');
  const [feedbackForm, setFeedbackForm] = useState({
    category: '',
    subject: '',
    message: '',
    rating: 0,
    anonymous: false
  });

  const [feedbackHistory] = useState([
    {
      id: 1,
      category: 'Event',
      subject: 'Alumni Networking Event Feedback',
      message: 'Great event! Would love to see more industry-specific networking sessions.',
      rating: 5,
      date: '2024-01-15',
      status: 'Reviewed',
      response: 'Thank you for your feedback! We\'ll consider industry-specific sessions for future events.'
    },
    {
      id: 2,
      category: 'Platform',
      subject: 'Website Improvement Suggestion',
      message: 'The alumni directory could benefit from better search filters.',
      rating: 4,
      date: '2024-01-10',
      status: 'In Progress',
      response: null
    },
    {
      id: 3,
      category: 'General',
      subject: 'Career Services Feedback',
      message: 'Excellent career counseling session. Very helpful for career transition.',
      rating: 5,
      date: '2024-01-05',
      status: 'Reviewed',
      response: 'We\'re glad you found the session helpful! Feel free to book follow-up sessions.'
    }
  ]);

  const categories = [
    'Event',
    'Platform',
    'Career Services',
    'Alumni Network',
    'General',
    'Suggestion'
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFeedbackForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleRatingChange = (rating) => {
    setFeedbackForm(prev => ({
      ...prev,
      rating
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Submitting feedback:', feedbackForm);
    // Here you would submit to backend
    alert('Feedback submitted successfully!');
    setFeedbackForm({
      category: '',
      subject: '',
      message: '',
      rating: 0,
      anonymous: false
    });
  };

  const StarRating = ({ rating, onRatingChange, readonly = false }) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => !readonly && onRatingChange(star)}
            className={`${readonly ? 'cursor-default' : 'cursor-pointer hover:scale-110'} transition-transform`}
          >
            <Star
              className={`w-5 h-5 ${
                star <= rating
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  const renderSubmitFeedback = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center mb-6">
        <MessageSquare className="w-6 h-6 text-blue-600 mr-3" />
        <h2 className="text-xl font-semibold text-gray-900">Submit Feedback</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              name="category"
              value={feedbackForm.category}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select a category</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Rating</label>
            <div className="flex items-center space-x-2">
              <StarRating rating={feedbackForm.rating} onRatingChange={handleRatingChange} />
              <span className="text-sm text-gray-600 ml-2">
                {feedbackForm.rating > 0 ? `${feedbackForm.rating}/5` : 'No rating'}
              </span>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
          <input
            type="text"
            name="subject"
            value={feedbackForm.subject}
            onChange={handleInputChange}
            required
            placeholder="Brief description of your feedback"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
          <textarea
            name="message"
            value={feedbackForm.message}
            onChange={handleInputChange}
            required
            rows={6}
            placeholder="Please provide detailed feedback..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            name="anonymous"
            checked={feedbackForm.anonymous}
            onChange={handleInputChange}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />
          <label className="ml-2 text-sm text-gray-700">Submit anonymously</label>
        </div>

        <button
          type="submit"
          className="w-full md:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center font-medium"
        >
          <Send className="w-4 h-4 mr-2" />
          Submit Feedback
        </button>
      </form>
    </div>
  );

  const renderFeedbackHistory = () => (
    <div className="space-y-6">
      {feedbackHistory.map((feedback) => (
        <div key={feedback.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h3 className="text-lg font-semibold text-gray-900">{feedback.subject}</h3>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  feedback.status === 'Reviewed' 
                    ? 'bg-green-100 text-green-700'
                    : 'bg-yellow-100 text-yellow-700'
                }`}>
                  {feedback.status}
                </span>
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                <span className="bg-gray-100 px-2 py-1 rounded">{feedback.category}</span>
                <span>{feedback.date}</span>
                <StarRating rating={feedback.rating} readonly />
              </div>
              <p className="text-gray-700 mb-3">{feedback.message}</p>
              
              {feedback.response && (
                <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mt-4">
                  <div className="flex items-center mb-2">
                    <User className="w-4 h-4 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-blue-900">Admin Response</span>
                  </div>
                  <p className="text-sm text-blue-800">{feedback.response}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <DashboardLayout activeTab="feedback">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Feedback Portal</h1>
          <p className="text-gray-600 mt-2">
            Share your thoughts and help us improve the alumni experience.
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8 border-b border-gray-200">
            <button
              onClick={() => setActiveTab('submit')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === 'submit'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Submit Feedback
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === 'history'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              My Feedback History
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'submit' ? renderSubmitFeedback() : renderFeedbackHistory()}
      </div>
    </DashboardLayout>
  );
};

export default FeedbackPortal;
